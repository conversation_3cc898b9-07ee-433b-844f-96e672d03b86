# EmailJS Setup Guide for Gmail Integration

## Step 1: Create EmailJS Account
1. Go to [EmailJS](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

## Step 2: Add Gmail Service
1. In your EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Select "Gmail"
4. Connect your Gmail account: **<EMAIL>**
5. Give your service a name (e.g., "Gmail Service")
6. Copy the **Service ID** and add it to your `.env` file as `REACT_APP_EMIAL_SERVICE_ID`

## Step 3: Create Email Template
1. Go to "Email Templates" in your dashboard
2. Click "Create New Template"
3. Use this template structure:

**Subject:** New Contact Form Message from {{firstname}}

**Body:**
```
Hello Rizwan,

You have received a new message from your portfolio contact form:

Name: {{name}}
Email: {{email}}
Subject: {{subject}}

Message:
{{message}}

---
This message was sent from your portfolio website.
```

4. Save the template and copy the **Template ID**
5. Add it to your `.env` file as `REACT_APP_TEMPLATE_ID`

## Step 4: Get Public Key
1. Go to "Account" in your EmailJS dashboard
2. Find your **Public Key** (User ID)
3. Add it to your `.env` file as `REACT_APP_PUBLIC_KEY`

## Step 5: Update .env File
Replace the placeholder values in your `.env` file:

```env
REACT_APP_EMIAL_SERVICE_ID=service_xxxxxxx
REACT_APP_TEMPLATE_ID=template_xxxxxxx
REACT_APP_PUBLIC_KEY=xxxxxxxxxxxxxxx
REACT_APP_RAPIDAPI_HOST=mailok-email-validation.p.rapidapi.com
REACT_APP_RAPIDAPI_KEY=your_rapidapi_key_here
```

## Step 6: Optional - Email Validation API
For email validation, you can:
1. Sign up at [RapidAPI](https://rapidapi.com/)
2. Subscribe to "Mailok Email Validation" API
3. Get your API key and add it to `REACT_APP_RAPIDAPI_KEY`

Or you can disable email validation by commenting out the validation code in the Contact component.

## Step 7: Test
1. Restart your development server: `npm start`
2. Go to the contact page
3. Fill out the form and submit
4. Check your Gmail inbox for the message

## Important Notes:
- All emails will be sent TO: <EMAIL>
- The form will show the sender's email in the message body
- Make sure to restart your development server after updating the .env file
- Never commit your .env file to version control (it's already in .gitignore)
