# 🚨 Quick Fix for Email Not Working

## Problem
Your contact form shows "Email service is not configured" because EmailJS credentials are missing.

## Quick Solution (5 minutes)

### Step 1: Go to EmailJS
1. Visit: https://www.emailjs.com/
2. Click "Sign Up" (it's FREE)
3. Use your Gmail: **<EMAIL>**

### Step 2: Add Gmail Service
1. After login, click "Email Services"
2. Click "Add New Service" 
3. Choose "Gmail"
4. Click "Connect Account" and login with **<EMAIL>**
5. Copy the **Service ID** (example: service_abc123)

### Step 3: Create Template
1. Go to "Email Templates"
2. Click "Create New Template"
3. Use this template:

**Subject:** New Contact from {{name}}

**Body:**
```
Hello Rizwan,

New message from your portfolio:

Name: {{name}}
Email: {{email}}
Subject: {{subject}}

Message:
{{message}}

Best regards,
Portfolio Contact Form
```

4. Save and copy the **Template ID** (example: template_xyz789)

### Step 4: Get Public Key
1. Go to "Account" tab
2. Copy your **Public Key** (example: abcdefghijk)

### Step 5: Update .env File
Replace these lines in your `.env` file:

```env
REACT_APP_EMIAL_SERVICE_ID=service_abc123
REACT_APP_TEMPLATE_ID=template_xyz789  
REACT_APP_PUBLIC_KEY=abcdefghijk
```

### Step 6: Restart Server
```bash
# Stop the current server (Ctrl+C)
# Then restart:
npm start
```

## Test It!
1. Go to http://localhost:3000/contact
2. Fill the form with any valid email
3. Click SEND
4. Check your Gmail inbox!

## Still Not Working?
- Make sure you restarted the server after updating .env
- Check browser console for errors (F12)
- Verify all three values are correctly copied from EmailJS dashboard

---
**All emails will be sent TO: <EMAIL>**
