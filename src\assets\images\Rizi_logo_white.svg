<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="50" viewBox="0 0 200 50" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="textGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f0f0f0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="50" fill="transparent"/>
  <text x="100" y="35" font-family="Arial, sans-serif" font-size="28" font-weight="bold" 
        text-anchor="middle" fill="url(#textGrad)" stroke="#cccccc" stroke-width="0.5">Rizi</text>
</svg>
