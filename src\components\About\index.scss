.stage-cube-cont {
  width: 50%;
  height: 100%;
  top: 0;
  padding-top: 18%;
  margin-left: 0;
  position: absolute;
  right: 0;
  overflow: hidden;
}

.cubespinner {
  animation-name: spincube;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
  animation-duration: 12s;
  transform-style: preserve-3d;
  transform-origin: 100px 100px 0;
  margin-left: calc(50% - 100px);

  div {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 1px solid #ccc;
    background: rgba(255, 255, 255, 0.4);
    text-align: center;
    font-size: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 0 20px 0px lightyellow;
  }

  .face1 {
    transform: translateZ(100px);
    color: #dd0031;
  }
  .face2 {
    transform: rotateY(90deg) translateZ(100px);
    color: #f06529;
  }
  .face3 {
    transform: rotateY(90deg) rotateX(90deg) translateZ(100px);
    color: #28a4d9;
  }
  .face4 {
    transform: rotateY(180deg) rotateZ(90deg) translateZ(100px);
    color: #5ed4f4;
  }
  .face5 {
    transform: rotateY(-90deg) rotateZ(90deg) translateZ(100px);
    color: #efd81d;
  }
  .face6 {
    transform: rotateX(-90deg) translateZ(100px);
    color: #ec4d28;
  }
}

@keyframes spincube {
  from,
  to {
    transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
  }
  16% {
    transform: rotateY(-90deg);
  }
  33% {
    transform: rotateY(-90deg) rotateZ(90deg);
  }
  50% {
    transform: rotateY(-180deg) rotateZ(90deg);
  }
  66% {
    transform: rotateY(-270deg) rotateX(90deg);
  }
  83% {
    transform: rotateX(90deg);
  }
}

// Responsive Design for About Page
@media screen and (max-width: 1200px) {
  .stage-cube-cont {
    width: 45%;
    padding-top: 15%;
  }

  .cubespinner {
    margin-left: calc(50% - 80px);
    transform-origin: 80px 80px 0;

    div {
      width: 160px;
      height: 160px;
      font-size: 80px;
    }

    .face1 { transform: translateZ(80px); }
    .face2 { transform: rotateY(90deg) translateZ(80px); }
    .face3 { transform: rotateY(90deg) rotateX(90deg) translateZ(80px); }
    .face4 { transform: rotateY(180deg) rotateZ(90deg) translateZ(80px); }
    .face5 { transform: rotateY(-90deg) rotateZ(90deg) translateZ(80px); }
    .face6 { transform: rotateX(-90deg) translateZ(80px); }
  }
}

@media screen and (max-width: 768px) {
  .stage-cube-cont {
    position: relative;
    width: 100%;
    height: 300px;
    padding-top: 50px;
    margin-top: 50px;
    right: auto;
  }

  .cubespinner {
    margin-left: calc(50% - 60px);
    transform-origin: 60px 60px 0;

    div {
      width: 120px;
      height: 120px;
      font-size: 60px;
      box-shadow: 0 0 15px 0px lightyellow;
    }

    .face1 { transform: translateZ(60px); }
    .face2 { transform: rotateY(90deg) translateZ(60px); }
    .face3 { transform: rotateY(90deg) rotateX(90deg) translateZ(60px); }
    .face4 { transform: rotateY(180deg) rotateZ(90deg) translateZ(60px); }
    .face5 { transform: rotateY(-90deg) rotateZ(90deg) translateZ(60px); }
    .face6 { transform: rotateX(-90deg) translateZ(60px); }
  }
}

@media screen and (max-width: 480px) {
  .stage-cube-cont {
    height: 250px;
    padding-top: 30px;
    margin-top: 30px;
  }

  .cubespinner {
    margin-left: calc(50% - 50px);
    transform-origin: 50px 50px 0;

    div {
      width: 100px;
      height: 100px;
      font-size: 50px;
      box-shadow: 0 0 10px 0px lightyellow;
    }

    .face1 { transform: translateZ(50px); }
    .face2 { transform: rotateY(90deg) translateZ(50px); }
    .face3 { transform: rotateY(90deg) rotateX(90deg) translateZ(50px); }
    .face4 { transform: rotateY(180deg) rotateZ(90deg) translateZ(50px); }
    .face5 { transform: rotateY(-90deg) rotateZ(90deg) translateZ(50px); }
    .face6 { transform: rotateX(-90deg) translateZ(50px); }
  }
}
