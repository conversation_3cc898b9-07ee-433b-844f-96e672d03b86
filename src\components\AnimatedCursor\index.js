import { useEffect, useState } from 'react'
import './index.scss'

const useMousePosition = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const updateMousePosition = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener('mousemove', updateMousePosition)

    return () => {
      window.removeEventListener('mousemove', updateMousePosition)
    }
  }, [])

  return mousePosition
}

const AnimatedCursor = () => {
  const { x, y } = useMousePosition()
  const [isHovering, setIsHovering] = useState(false)
  const [hoverTarget, setHoverTarget] = useState(null)

  useEffect(() => {
    // Hide default cursor on desktop
    document.body.style.cursor = 'none'

    // Add hover listeners to interactive elements
    const interactiveElements = document.querySelectorAll(
      'a, button, .nav-link, .flat-button, .logo, [role="button"], input, textarea'
    )

    const handleMouseEnter = (e) => {
      setIsHovering(true)
      setHoverTarget(e.target)
    }

    const handleMouseLeave = () => {
      setIsHovering(false)
      setHoverTarget(null)
    }

    interactiveElements.forEach((el) => {
      el.addEventListener('mouseenter', handleMouseEnter)
      el.addEventListener('mouseleave', handleMouseLeave)
    })

    return () => {
      document.body.style.cursor = 'auto'
      interactiveElements.forEach((el) => {
        el.removeEventListener('mouseenter', handleMouseEnter)
        el.removeEventListener('mouseleave', handleMouseLeave)
      })
    }
  }, [])

  // Don't render on mobile/tablet
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  if (isMobile) return null

  return (
    <>
      {/* Main cursor dot */}
      <div
        className="custom-cursor"
        style={{
          left: `${x}px`,
          top: `${y}px`,
        }}
      />
      
      {/* Trailing cursor */}
      <div
        className={`custom-cursor-trail ${isHovering ? 'hovering' : ''}`}
        style={{
          left: `${x}px`,
          top: `${y}px`,
        }}
      />

      {/* Hover highlight blob */}
      {isHovering && hoverTarget && (
        <div
          className="cursor-hover-blob"
          style={{
            left: `${x}px`,
            top: `${y}px`,
          }}
        />
      )}
    </>
  )
}

export default AnimatedCursor
