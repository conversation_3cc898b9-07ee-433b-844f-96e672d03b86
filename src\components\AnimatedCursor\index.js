import { useEffect, useState } from 'react'
import './index.scss'

const useMousePosition = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [velocity, setVelocity] = useState({ x: 0, y: 0 })
  const [rotation, setRotation] = useState(0)

  useEffect(() => {
    let lastX = 0
    let lastY = 0
    let lastTime = Date.now()
    let animationFrame

    const updateMousePosition = (e) => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }

      animationFrame = requestAnimationFrame(() => {
        const currentTime = Date.now()
        const deltaTime = currentTime - lastTime

        if (deltaTime > 0) {
          const deltaX = e.clientX - lastX
          const deltaY = e.clientY - lastY

          // Calculate velocity
          const velocityX = deltaX / deltaTime
          const velocityY = deltaY / deltaTime

          // Calculate rotation based on movement direction with smoothing
          const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI)

          setMousePosition({ x: e.clientX, y: e.clientY })
          setVelocity({ x: velocityX, y: velocityY })
          setRotation(prevRotation => {
            // Smooth rotation transition
            const diff = angle - prevRotation
            const adjustedDiff = ((diff + 180) % 360) - 180
            return prevRotation + adjustedDiff * 0.1
          })

          lastX = e.clientX
          lastY = e.clientY
          lastTime = currentTime
        }
      })
    }

    window.addEventListener('mousemove', updateMousePosition)

    return () => {
      window.removeEventListener('mousemove', updateMousePosition)
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [])

  return { position: mousePosition, velocity, rotation }
}

const AnimatedCursor = () => {
  const { position, velocity, rotation } = useMousePosition()
  const [isHovering, setIsHovering] = useState(false)
  const [hoverTarget, setHoverTarget] = useState(null)
  const [isMoving, setIsMoving] = useState(false)

  // Detect movement for animation states
  useEffect(() => {
    const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y)
    setIsMoving(speed > 0.1)
  }, [velocity])

  useEffect(() => {
    // Hide default cursor on desktop
    document.body.style.cursor = 'none'

    // Use event delegation for better performance
    const handleMouseOver = (e) => {
      const target = e.target
      const isInteractive = target.matches('a, button, .nav-link, .flat-button, .logo, [role="button"], input, textarea, .tagcloud--item, svg, .contact-form input, .contact-form textarea, .contact-form button, .home-link, .about-link, .contact-link, .skills-link') ||
                          target.closest('a, button, .nav-link, .flat-button, .logo, [role="button"], nav')

      if (isInteractive && !isHovering) {
        setIsHovering(true)
        setHoverTarget(target)
      } else if (!isInteractive && isHovering) {
        setIsHovering(false)
        setHoverTarget(null)
      }
    }

    const handleMouseOut = (e) => {
      const target = e.target
      const relatedTarget = e.relatedTarget

      // Check if we're moving to a non-interactive element
      if (relatedTarget && !relatedTarget.matches('a, button, .nav-link, .flat-button, .logo, [role="button"], input, textarea, .tagcloud--item, svg') &&
          !relatedTarget.closest('a, button, .nav-link, .flat-button, .logo, [role="button"], nav')) {
        setIsHovering(false)
        setHoverTarget(null)
      }
    }

    // Add event delegation listeners
    document.addEventListener('mouseover', handleMouseOver)
    document.addEventListener('mouseout', handleMouseOut)

    return () => {
      document.body.style.cursor = 'auto'
      document.removeEventListener('mouseover', handleMouseOver)
      document.removeEventListener('mouseout', handleMouseOut)
    }
  }, [])

  // Don't render on mobile/tablet
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  if (isMobile) return null

  return (
    <>


      {/* Main cursor dot */}
      <div
        className={`custom-cursor ${isMoving ? 'moving' : ''} ${isHovering ? 'hovering' : ''}`}
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          transform: `translate(-50%, -50%) rotate(${rotation}deg)`,
        }}
      />

      {/* Trailing cursor */}
      <div
        className={`custom-cursor-trail ${isHovering ? 'hovering' : ''} ${isMoving ? 'moving' : ''}`}
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          transform: `translate(-50%, -50%) rotate(${rotation * 0.5}deg)`,
        }}
      />

      {/* Hover highlight blob */}
      {isHovering && (
        <div
          className="cursor-hover-blob"
          style={{
            left: `${position.x}px`,
            top: `${position.y}px`,
            transform: `translate(-50%, -50%) rotate(${rotation * 0.3}deg)`,
          }}
        />
      )}
    </>
  )
}

export default AnimatedCursor
