// Custom Animated Cursor Styles
.custom-cursor {
  position: fixed;
  width: 8px;
  height: 8px;
  background: linear-gradient(45deg, #ffd700, #ff8c00);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  mix-blend-mode: difference;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);

  &.moving {
    width: 12px;
    height: 12px;
    background: linear-gradient(45deg, #ff4500, #ffd700);
    box-shadow:
      0 0 15px rgba(255, 69, 0, 0.6),
      0 0 30px rgba(255, 215, 0, 0.3);
  }

  &.hovering {
    width: 16px;
    height: 16px;
    background: linear-gradient(45deg, #00ff7f, #ffd700);
    box-shadow:
      0 0 20px rgba(0, 255, 127, 0.7),
      0 0 40px rgba(255, 215, 0, 0.4);
  }
}

.custom-cursor-trail {
  position: fixed;
  width: 32px;
  height: 32px;
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: radial-gradient(
    circle,
    rgba(255, 215, 0, 0.1) 0%,
    transparent 70%
  );

  &.moving {
    width: 40px;
    height: 40px;
    border-color: rgba(255, 69, 0, 0.5);
    background: radial-gradient(
      circle,
      rgba(255, 69, 0, 0.15) 0%,
      rgba(255, 215, 0, 0.05) 50%,
      transparent 70%
    );
    animation: trailRotate 2s linear infinite;
  }

  &.hovering {
    width: 48px;
    height: 48px;
    border-color: rgba(0, 255, 127, 0.6);
    background: radial-gradient(
      circle,
      rgba(0, 255, 127, 0.2) 0%,
      rgba(255, 215, 0, 0.1) 50%,
      transparent 70%
    );
    animation: trailPulse 1.5s ease-in-out infinite;
  }
}

.cursor-hover-blob {
  position: fixed;
  width: 60px;
  height: 60px;
  background: radial-gradient(
    circle,
    rgba(255, 69, 0, 0.3) 0%,
    rgba(255, 215, 0, 0.15) 40%,
    rgba(0, 255, 127, 0.1) 70%,
    transparent 100%
  );
  border-radius: 50%;
  pointer-events: none;
  z-index: 9997;
  transition: all 0.3s ease-out;
  animation: blobRotate 3s linear infinite, blobPulse 2s ease-in-out infinite;
}

// Rotation animations
@keyframes trailRotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes trailPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.5;
  }
}

@keyframes blobRotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes blobPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
    opacity: 0.4;
  }
}

// Enhanced hover effects for navigation items
.nav-link {
  position: relative;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 40px;
      height: 40px;
      background: radial-gradient(
        circle,
        rgba(255, 69, 0, 0.2) 0%,
        transparent 70%
      );
      border-radius: 50%;
      transform: translate(-50%, -50%);
      z-index: -1;
      animation: navHoverGlow 0.3s ease-out forwards;
    }
  }
}

@keyframes navHoverGlow {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

// Enhanced button hover effects
.flat-button {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(
      circle,
      rgba(255, 215, 0, 0.3) 0%,
      transparent 70%
    );
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.4s ease;
    z-index: -1;
  }
  
  &:hover::before {
    width: 200px;
    height: 200px;
  }
}

// Logo hover enhancement
.logo {
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05) rotate(5deg);
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.5));
  }
}

// Responsive cursor adjustments
@media screen and (max-width: 1200px) {
  .custom-cursor {
    width: 6px;
    height: 6px;

    &.moving {
      width: 10px;
      height: 10px;
    }

    &.hovering {
      width: 14px;
      height: 14px;
    }
  }

  .custom-cursor-trail {
    width: 28px;
    height: 28px;

    &.moving {
      width: 36px;
      height: 36px;
    }

    &.hovering {
      width: 42px;
      height: 42px;
    }
  }
}

// Disable cursor effects on mobile and tablets
@media screen and (max-width: 768px) {
  .custom-cursor,
  .custom-cursor-trail,
  .cursor-hover-blob {
    display: none !important;
  }

  .nav-link:hover::before,
  .flat-button::before {
    display: none;
  }

  // Re-enable default cursor
  body {
    cursor: auto !important;
  }
}
