// Custom Animated Cursor Styles
.custom-cursor {
  position: fixed;
  width: 8px;
  height: 8px;
  background: #ffd700;
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transform: translate(-50%, -50%);
  transition: all 0.1s ease-out;
  mix-blend-mode: difference;
}

.custom-cursor-trail {
  position: fixed;
  width: 32px;
  height: 32px;
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  transform: translate(-50%, -50%);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  &.hovering {
    width: 48px;
    height: 48px;
    border-color: rgba(255, 69, 0, 0.6);
    background: rgba(255, 69, 0, 0.1);
  }
}

.cursor-hover-blob {
  position: fixed;
  width: 60px;
  height: 60px;
  background: radial-gradient(
    circle,
    rgba(255, 69, 0, 0.2) 0%,
    rgba(255, 215, 0, 0.1) 50%,
    transparent 100%
  );
  border-radius: 50%;
  pointer-events: none;
  z-index: 9997;
  transform: translate(-50%, -50%);
  transition: all 0.2s ease-out;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.4;
  }
}

// Enhanced hover effects for navigation items
.nav-link {
  position: relative;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 40px;
      height: 40px;
      background: radial-gradient(
        circle,
        rgba(255, 69, 0, 0.2) 0%,
        transparent 70%
      );
      border-radius: 50%;
      transform: translate(-50%, -50%);
      z-index: -1;
      animation: navHoverGlow 0.3s ease-out forwards;
    }
  }
}

@keyframes navHoverGlow {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

// Enhanced button hover effects
.flat-button {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(
      circle,
      rgba(255, 215, 0, 0.3) 0%,
      transparent 70%
    );
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.4s ease;
    z-index: -1;
  }
  
  &:hover::before {
    width: 200px;
    height: 200px;
  }
}

// Logo hover enhancement
.logo {
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05) rotate(5deg);
    filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.5));
  }
}

// Disable cursor effects on mobile
@media screen and (max-width: 768px) {
  .custom-cursor,
  .custom-cursor-trail,
  .cursor-hover-blob {
    display: none !important;
  }
  
  .nav-link:hover::before,
  .flat-button::before {
    display: none;
  }
}
