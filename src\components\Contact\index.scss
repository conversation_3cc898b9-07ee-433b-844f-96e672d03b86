.contact-form {
  width: 100%;
  margin-top: 20px;

  ul {
    padding: 0;
    margin: 0;

    li {
      padding: 0;
      margin: 0;
      border-radius: 0.5rem;
      list-style: none;
      margin-bottom: 10px;
      opacity: 0;
      overflow: hidden;
      display: block;
      clear: both;
      position: relative;
      animation: fadeInUp 2s 2s;
      animation-fill-mode: forwards;
    }

    li.half {
      width: 49%;
      margin-left: 2%;
      float: left;
      clear: none;

      &:first-child {
        margin-left: 0;
      }
    }
  }

  input[type='text'],
  input[type='email'] {
    width: 100%;
    border: 0;
    background: #353535;
    height: 50px;
    font-size: 16px;
    color: #fff;
    padding: 0 20px;
    box-sizing: border-box;
  }

  textarea {
    width: 100%;
    border: 0;
    background: #353535;
    height: 50px;
    font-size: 16px;
    color: #fff;
    padding: 20px;
    box-sizing: border-box;
    min-height: 150px;
  }

  .flat-button {
    color: #ff4500;
    font-size: 11px;
    letter-spacing: 3px;
    text-decoration: none;
    padding: 8px 10px;
    border: 1px solid #ff4500;
    float: left;
    border-radius: 4px;
    background: 0 0;
    text-transform: uppercase;
    float: right;
    text-align: center;
    margin-right: 10px;

    &:hover {
      cursor: pointer;
      background-color: #ff4500;
      color: #000;
    }
  }
}

.map-wrap {
  position: relative;
  background: rgba(8, 253, 216, 0.1);
  float: right;
  width: 53%;
  height: 115%;
  margin-top: -3%;
}

.leaflet-container {
  position: relative;
  width: 100%;
  height: 100%;
  opacity: 0;
  animation: backInRight 1s 1.2s;
  animation-fill-mode: forwards;
}

.info-map {
  position: absolute;
  background: #000;
  top: 2%;
  left: 6%;
  z-index: 1;
  width: 30%;
  padding: 3%;
  color: #fff;
  font-family: 'Helvetica';
  font-size: 17px;
  font-weight: 300;
  opacity: 0;
  border-radius: 0.5rem;
  animation: fadeIn 1s 1.5s;
  animation-fill-mode: forwards;

  span {
    font-size: 16px;

    span {
      color: #ffd700;
    }
  }
}

// Responsive Design for Contact Page
@media screen and (max-width: 1200px) {
  .map-wrap {
    width: 45%;
  }

  .info-map {
    width: 35%;
    font-size: 15px;
  }
}

@media screen and (max-width: 768px) {
  .contact-form {
    ul {
      li.half {
        width: 100%;
        margin-left: 0;
        float: none;
      }
    }

    input[type='text'],
    input[type='email'] {
      height: 45px;
      font-size: 14px;
      padding: 0 15px;
    }

    textarea {
      height: 120px;
      font-size: 14px;
      padding: 15px;
    }

    .flat-button {
      font-size: 12px;
      padding: 8px 15px;
      letter-spacing: 3px;
    }
  }

  .map-wrap {
    float: none;
    width: 100%;
    height: 300px;
    margin-top: 50px;
  }

  .info-map {
    position: relative;
    width: 100%;
    top: 0;
    left: 0;
    margin-bottom: 20px;
    font-size: 14px;
    padding: 20px;
  }
}

@media screen and (max-width: 480px) {
  .contact-form {
    input[type='text'],
    input[type='email'] {
      height: 40px;
      font-size: 13px;
      padding: 0 12px;
    }

    textarea {
      height: 100px;
      font-size: 13px;
      padding: 12px;
    }

    .flat-button {
      font-size: 11px;
      padding: 6px 12px;
      letter-spacing: 2px;
    }
  }

  .map-wrap {
    height: 250px;
    margin-top: 30px;
  }

  .info-map {
    font-size: 13px;
    padding: 15px;
  }
}
