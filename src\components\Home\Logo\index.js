/* eslint-disable no-unused-vars */
import { useRef } from 'react'

import LogoR from '../../../assets/images/logo-r.svg'
import './index.scss'

const Logo = () => {
  const bgRef = useRef()
  const outlineLogoRef = useRef()
  const solidLogoRef = useRef()

  return (
    <div className="logo-container" ref={bgRef}>
      <img
        className="solid-logo"
        ref={solidLogoRef}
        src={LogoR}
        draggable="false"
        alt="Rizwan, Frontend Developer"
      />

      <svg
        width="559pt"
        height="897pt"
        version="1.0"
        viewBox="0 0 559 897"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          className="svg-container"
          transform="translate(0 457) scale(.1 -.1)"
          fill="none"
        >
          <path
            ref={outlineLogoRef}
            d="M1500 4500 L1500 1000 L2000 1000 L2000 1500 L2800 1500 Q3200 1500 3400 1700 Q3600 1900 3600 2300 Q3600 2700 3400 2900 Q3200 3100 2800 3100 L2400 3100 L3600 4500 L3000 4500 L2000 3300 L2000 4500 L1500 4500 Z M2000 2700 L2800 2700 Q2900 2700 2950 2650 Q3000 2600 3000 2500 Q3000 2400 2950 2350 Q2900 2300 2800 2300 L2000 2300 L2000 2700 Z"
          />
        </g>
      </svg>
    </div>
  )
}

export default Logo
