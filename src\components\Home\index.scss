.home-page {
  position: relative;
  z-index: 1;

  .text-zone {
    position: absolute;
    left: 10%;
    top: 50%;
    transform: translateY(-50%);
    width: 40%;
    max-height: 90%;
    z-index: 2;

    h1 {
      color: #fff;
      font-size: 56px;
      line-height: 63px;
      margin: 0;
      font-family: 'Coolvetica';
      font-weight: 400;

      &:before {
        content: '<h1>';
        font-family: 'La Belle Aurore', cursive;
        color: #00ff7f;
        font-size: 18px;
        position: absolute;
        margin-top: -40px;
        left: -15px;
        opacity: 0.9;
      }

      &:after {
        content: '<h1/>';
        font-family: 'La Belle Aurore', cursive;
        color: #00ff7f;
        font-size: 18px;
        position: absolute;
        margin-top: 18px;
        margin-left: 20px;
        animation: fadeIn 1s 1.7s backwards;
        opacity: 0.9;
      }
    }

    img {
      margin-left: 20px;
      opacity: 0;
      width: 32px;
      height: auto;
      animation: rotateIn 1s linear both;
      animation-delay: 1.4s;
      user-select: none;
    }
  }

  h2 {
    color: #8d8d8d;
    margin-top: 20px;
    font-weight: 400;
    font-size: 11px;
    font-family: sans-serif;
    letter-spacing: 3px;
    animation: fadeIn 1s 1.8s backwards;
  }

  .flat-button {
    color: #00ff7f;
    font-size: 13px;
    font-weight: 400;
    letter-spacing: 4px;
    font-family: sans-serif;
    text-decoration: none;
    padding: 10px 18px;
    border: 1px solid #00ff7f;
    margin-top: 25px;
    float: left;
    animation: fadeInAnimation 1s 1.8s backwards;
    white-space: nowrap;
    border-radius: 0.5rem;

    &:hover {
      background: #00ff7f;
      color: rgb(0, 0, 0);
    }
  }

  .selectDisable {
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
  }
}

// Responsive Design
@media screen and (max-width: 1200px) {
  .home-page {
    .text-zone {
      left: 8%;
      width: 45%;

      h1 {
        font-size: 48px;
        line-height: 55px;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .home-page {
    .text-zone {
      position: relative;
      left: 0;
      top: 0;
      transform: none;
      width: 100%;
      padding: 20px;
      text-align: center;
      margin-top: 50px;

      h1 {
        font-size: 36px;
        line-height: 42px;

        &:before {
          font-size: 14px;
          margin-top: -30px;
          left: -10px;
        }

        &:after {
          font-size: 14px;
          margin-top: 15px;
          margin-left: 15px;
        }
      }
    }

    h2 {
      font-size: 10px;
      letter-spacing: 2px;
      text-align: center;
      margin-top: 15px;
    }

    .flat-button {
      display: block;
      margin: 20px auto 0;
      float: none;
      text-align: center;
      width: fit-content;
    }
  }
}

@media screen and (max-width: 480px) {
  .home-page {
    .text-zone {
      padding: 15px;
      margin-top: 30px;

      h1 {
        font-size: 28px;
        line-height: 34px;

        &:before {
          font-size: 12px;
          margin-top: -25px;
          left: -8px;
        }

        &:after {
          font-size: 12px;
          margin-top: 12px;
          margin-left: 12px;
        }
      }
    }

    h2 {
      font-size: 9px;
      letter-spacing: 1px;
      line-height: 16px;
    }

    .flat-button {
      font-size: 11px;
      padding: 8px 15px;
      letter-spacing: 3px;
    }
  }
}

