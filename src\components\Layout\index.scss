.page {
  width: 100%;
  height: 100%;
  user-select: none;
}

.top-tags {
  bottom: auto;
  top: 35px;
}

.tags {
  // color: #ffd700;
  // color: #ff4500; // red
  color: #00ff7f; // green
  opacity: 0.9;
  position: absolute;
  bottom: 0;
  left: 120px;
  font-size: 18px;
  font-family: 'La Belle Aurore', cursive;
}

.bottom-tag-html {
  margin-left: -20px;
}

.container {
  width: 100%;
  will-change: contents;
  height: 90%;
  min-height: 566px;
  position: absolute;
  opacity: 0;
  top: 5%;
  margin: 0 auto;
  z-index: 1;
  transform-style: preserve-3d;
  animation: fadeIn 1s forwards;
  animation-delay: 1s;
}

.container.contact-page,
.container.about-page,
.container.skills-page {
  .text-zone {
    position: absolute;
    left: 10%;
    top: 50%;
    transform: translateY(-50%);
    width: 35%;
    vertical-align: middle;
    display: table-cell;
    max-height: 90%;

    h1 {
      font-size: 53px;
      font-family: 'Cool<PERSON><PERSON>';
      color: #00ff7f;
      font-weight: 400;
      margin-top: 0;
      position: relative;
      margin-bottom: 40px;
      left: -10px;

      &:before {
        content: '<h1>';
        font-family: 'La Belle Aurore', cursive;
        color: #00ff7f;
        font-size: 18px;
        position: absolute;
        margin-top: -10px;
        left: -10px;
        opacity: 0.6;
        line-height: 18px;
      }

      &:after {
        content: '<h1/>';
        font-family: 'La Belle Aurore', cursive;
        color: #00ff7f;
        font-size: 18px;
        line-height: 18px;
        position: absolute;
        left: -30px;
        bottom: -20px;
        margin-left: 20px;
        opacity: 0.6;
      }
    }

    p {
      font-size: 17px;
      color: #fff;
      font-family: sans-serif;
      font-weight: 300;
      max-width: fit-content;
      animation: pulse 1s;
      &:nth-of-type(1) {
        animation-delay: 1.1s;
      }
      &:nth-of-type(2) {
        animation-delay: 1.2s;
      }
      &:nth-of-type(3) {
        animation-delay: 1.3s;
      }
    }
  }

  .text-animate-hover {
    &:hover {
      color: #ff4500;
    }
  }
}

// n is number of stars required
@function multiple-box-shadow($n) {
  $value: '#{random(2000)}px #{random(2000)}px #FFF';
  @for $i from 2 through $n {
    $value: '#{$value} , #{random(2000)}px #{random(2000)}px #FFF';
  }
  @return unquote($value);
}

$shadows-small: multiple-box-shadow(1400);
$shadows-medium: multiple-box-shadow(400);
$shadows-big: multiple-box-shadow(200);

html {
  height: 100%;
  // background: radial-gradient(ellipse at bottom, #1B2735 0%, #090A0F 100%);
  overflow: hidden;
}
#stars {
  width: 1px;
  height: 1px;
  background: transparent;
  box-shadow: $shadows-small;
  animation: animStar 50s linear infinite;

  &:after {
    content: ' ';
    position: absolute;
    top: 2000px;
    width: 1px;
    height: 1px;
    background: transparent;
    box-shadow: $shadows-small;
  }
}
#stars2 {
  width: 2px;
  height: 2px;
  background: transparent;
  box-shadow: $shadows-medium;
  animation: animStar 100s linear infinite;

  &:after {
    content: ' ';
    position: absolute;
    top: 2000px;
    width: 2px;
    height: 2px;
    background: transparent;
    box-shadow: $shadows-medium;
  }
}
#stars3 {
  width: 3px;
  height: 3px;
  background: transparent;
  box-shadow: $shadows-big;
  animation: animStar 150s linear infinite;

  &:after {
    content: ' ';
    position: absolute;
    top: 2000px;
    width: 3px;
    height: 3px;
    background: transparent;
    box-shadow: $shadows-big;
  }
}

@keyframes animStar {
  from {
    transform: translateY(0px);
  }
  to {
    transform: translateY(-2000px);
  }
}

// Responsive Design for Layout
@media screen and (max-width: 768px) {
  .page {
    padding-bottom: 60px; // Space for mobile navigation
  }

  .tags {
    left: 20px;
    font-size: 14px;
  }

  .container {
    height: calc(100% - 60px);
    top: 0;
    padding: 20px;
    min-height: auto;
  }

  .container.contact-page,
  .container.about-page,
  .container.skills-page {
    .text-zone {
      position: relative;
      left: 0;
      top: 0;
      transform: none;
      width: 100%;
      display: block;
      text-align: center;
      padding: 20px 0;

      h1 {
        font-size: 36px;
        margin-bottom: 30px;
        left: 0;

        &:before {
          font-size: 14px;
          margin-top: -8px;
          left: -8px;
        }

        &:after {
          font-size: 14px;
          left: -25px;
          bottom: -15px;
        }
      }

      p {
        font-size: 15px;
        text-align: left;
        max-width: 100%;
        margin-bottom: 15px;
      }
    }
  }
}

@media screen and (max-width: 480px) {
  .tags {
    left: 15px;
    font-size: 12px;
  }

  .container {
    padding: 15px;
  }

  .container.contact-page,
  .container.about-page,
  .container.skills-page {
    .text-zone {
      padding: 15px 0;

      h1 {
        font-size: 28px;
        margin-bottom: 25px;

        &:before {
          font-size: 12px;
          margin-top: -6px;
          left: -6px;
        }

        &:after {
          font-size: 12px;
          left: -20px;
          bottom: -12px;
        }
      }

      p {
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}
