#tsparticles {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: auto;
}

.home-page {
  position: relative;
  z-index: 1;
}

// Responsive Design for Particles
@media screen and (max-width: 768px) {
  #tsparticles {
    // Reduce particle density on mobile for better performance
    canvas {
      opacity: 0.7;
    }
  }
}

@media screen and (max-width: 480px) {
  #tsparticles {
    canvas {
      opacity: 0.5;
    }
  }
}
