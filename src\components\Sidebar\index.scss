.nav-bar {
  background: #181818;
  width: 60px;
  height: 100%;
  position: absolute;
  top: 0;
  z-index: 3;
  min-height: 500px;

  .logo {
    display: block;
    padding: 8px 0;

    img {
      display: block;
      margin: 8px auto;
      width: 24px;
      height: auto;

      &.sub-logo {
        width: 50px;
      }
    }
  }

  nav {
    display: block;
    text-align: center;
    position: absolute;
    height: 210px;
    top: 50%;
    margin-top: -120px;
    width: 100%;

    a {
      font-size: 22px;
      color: #4d4d4e;
      display: block;
      line-height: 51px;
      height: 51px;
      position: relative;
      text-decoration: none;

      i {
        transition: all 0.3s ease-out;
      }

      &:hover {
        color: #ffd700;

        svg {
          opacity: 0;
        }

        &:after {
          opacity: 1;
        }
      }

      &:after {
        content: '';
        font-size: 9px;
        letter-spacing: 2px;
        position: absolute;
        bottom: 0;
        display: block;
        width: 100%;
        text-align: center;
        opacity: 0;
        -webkit-transition: all 0.3s ease-out;
        transition: all 0.3s ease-out;
      }

      &:first-child {
        &:after {
          content: 'HOME';
        }
      }
    }

    a.about-link {
      &:after {
        content: 'ABOUT';
      }
    }

    a.contact-link {
      &:after {
        content: 'CONTACT';
      }
    }

    a.skills-link {
      &:after {
        content: 'SKILLS';
      }
    }

    a.active {
      svg {
        color: #ffd700;
      }
    }
  }

  ul {
    position: absolute;
    bottom: 20px;
    width: 100%;
    display: block;
    padding: 0;
    list-style: none;
    text-align: center;
    margin: 0;

    li {
      a {
        padding: 7px 0;
        display: block;
        font-size: 15px;
        line-height: 16px;
        color: #4d4d4e;

        &:hover {
          color: #ffd700;
        }
      }
    }
  }
}

.random {
  color: #b9b9b9;
}

// Responsive Design for Sidebar
@media screen and (max-width: 768px) {
  .nav-bar {
    width: 100%;
    height: 60px;
    position: fixed;
    bottom: 0;
    top: auto;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    min-height: auto;

    .logo {
      padding: 0;

      img {
        width: 20px;
        margin: 0;

        &.sub-logo {
          width: 40px;
        }
      }
    }

    nav {
      position: static;
      height: auto;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 20px;

      a {
        font-size: 18px;
        line-height: 1;
        height: auto;
        margin: 0;

        &:after {
          display: none;
        }
      }
    }

    ul {
      position: static;
      bottom: auto;
      display: flex;
      align-items: center;
      gap: 15px;
      margin: 0;

      li {
        a {
          padding: 0;
          font-size: 14px;
          line-height: 1;
        }
      }
    }
  }
}

@media screen and (max-width: 480px) {
  .nav-bar {
    padding: 0 15px;

    .logo {
      img {
        width: 18px;

        &.sub-logo {
          width: 35px;
        }
      }
    }

    nav {
      gap: 15px;

      a {
        font-size: 16px;
      }
    }

    ul {
      gap: 10px;

      li {
        a {
          font-size: 12px;
        }
      }
    }
  }
}
