import { useEffect, useState } from 'react'

import Loader from 'react-loaders'

import WordCloud from './wordcloud'
import AnimatedLetters from '../AnimatedLetters'
import './index.scss'

const Skills = () => {
  const [letterClass, setLetterClass] = useState('text-animate')

  const skillsArray = 'Skills'.split('')

  useEffect(() => {
    const timer = setTimeout(() => {
      setLetterClass('text-animate-hover')
    }, 2000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <>
      <div className="container skills-page">
        <div className="text-zone">
          <h1>
            <AnimatedLetters
              letterClass={letterClass}
              strArray={skillsArray}
              idx={15}
            />
            <br />
          </h1>
          <p>
            I have a strong foundation in frontend development, with
            a focus on creating beautiful, responsive user interfaces. My experience
            includes building modern web applications, implementing interactive features,
            and working with cutting-edge frontend frameworks to deliver
            exceptional user experiences.
          </p>
          <p>
            My skill set spans across React, Angular, modern JavaScript,
            and responsive design. I’m committed to staying
            updated with the latest frontend technologies and continually refining my
            expertise to create amazing user experiences.
          </p>
        </div>

        <div className="tagcloud-wrap">
          <WordCloud />
        </div>
      </div>

      <Loader type="pacman" />
    </>
  )
}

export default Skills
