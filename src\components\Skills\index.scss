.tagcloud-wrap {
  // background: gray;
  float: right;
  width: 53%;
  height: 115%;
  margin-top: -3%;

  .tagcloud {
    color: #ff4500;
    font-family: 'Poppins', sans-serif;
    font-size: 20px;
    font-weight: 650;
    margin: 10% auto;
  }
  .tagcloud--item:hover {
    color: #00ff7f;
  }
}

// Responsive Design for Skills Page
@media screen and (max-width: 1200px) {
  .tagcloud-wrap {
    width: 50%;

    .tagcloud {
      font-size: 18px;
      margin: 8% auto;
    }
  }
}

@media screen and (max-width: 768px) {
  .tagcloud-wrap {
    float: none;
    width: 100%;
    height: 400px;
    margin-top: 50px;

    .tagcloud {
      font-size: 16px;
      margin: 5% auto;
      max-width: 90%;
    }
  }
}

@media screen and (max-width: 480px) {
  .tagcloud-wrap {
    height: 300px;
    margin-top: 30px;

    .tagcloud {
      font-size: 14px;
      margin: 3% auto;
      max-width: 95%;
    }
  }
}
