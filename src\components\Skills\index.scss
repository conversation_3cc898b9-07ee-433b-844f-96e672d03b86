.tagcloud-wrap {
  float: right;
  width: 53%;
  height: 115%;
  margin-top: -3%;
  position: relative;

  .main {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .content {
    width: 100%;
    height: 100%;
    display: block;
  }

  .tagcloud {
    color: #ff4500 !important;
    font-family: 'Poppins', sans-serif !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    width: 100% !important;
    height: 100% !important;
  }

  .tagcloud--item {
    color: #ff4500 !important;
    font-family: 'Poppins', sans-serif !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: color 0.3s ease !important;
  }

  .tagcloud--item:hover {
    color: #00ff7f !important;
  }
}

// Responsive Design for Skills Page
@media screen and (max-width: 1200px) {
  .tagcloud-wrap {
    width: 45%;
    margin-top: 0;

    .tagcloud {
      font-size: 16px !important;
    }

    .tagcloud--item {
      font-size: 16px !important;
    }
  }
}

@media screen and (max-width: 768px) {
  .tagcloud-wrap {
    float: none;
    width: 100%;
    height: 350px;
    margin-top: 30px;
    order: 2;

    .main {
      height: 100%;
    }

    .content {
      height: 100%;
    }

    .tagcloud {
      font-size: 14px !important;
      height: 100% !important;
    }

    .tagcloud--item {
      font-size: 14px !important;
    }
  }
}

@media screen and (max-width: 480px) {
  .tagcloud-wrap {
    height: 300px;
    margin-top: 30px;

    .tagcloud {
      font-size: 12px !important;
    }

    .tagcloud--item {
      font-size: 12px !important;
    }
  }
}
