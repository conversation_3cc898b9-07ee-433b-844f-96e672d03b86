import React, { useEffect, useState } from 'react'

import TagCloud from 'TagCloud'

const WordCloud = () => {
  const [isLoading, setLoad] = useState(true)

  const container = '.content'
  const texts = [
    'React',
    'Angular',
    'JavaScript',
    'TypeScript',
    'HTML5',
    'CSS3',
    'SASS',
    'NextJS',
    'Vue.js',
    'Redux',
    'React Router',
    'Material-UI',
    'Bootstrap',
    'Tailwind CSS',
    'Webpack',
    'Vite',
    'ESLint',
    'Prettier',
    'Jest',
    'Cypress',
    'Figma',
    'Adobe XD',
    'Responsive Design',
    'Mobile First',
    'Progressive Web Apps',
    'Web Accessibility',
    'Git',
    'GitHub',
    'VS Code',
    'Chrome DevTools',
    'NPM',
    'Yarn',
    'REST APIs',
    'GraphQL',
    'Firebase',
    'Netlify',
    'Vercel',
    'Node.js',
    'Express',
    'MongoDB',
  ]
  const options = {
    radius: 300,
    // animation speed
    // slow, normal, fast
    maxSpeed: 'fast',
    initSpeed: 'fast',
    // 0 = top
    // 90 = left
    // 135 = right-bottom
    direction: 135,
    // interact with cursor move on mouse out
    keep: true,
  }
  //   to render wordcloud each time the page is reloaded
  useEffect(() => {
    if (isLoading) {
      TagCloud(container, texts, options)
      setLoad(false)
    }
  })

  return (
    <div className="main">
      <span className="content"></span>
    </div>
  )
}

export default WordCloud
