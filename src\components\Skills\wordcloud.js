import React, { useEffect, useState } from 'react'

import TagCloud from 'TagCloud'

const WordCloud = () => {
  const [isLoading, setLoad] = useState(true)

  const container = '.content'
  const texts = [
    'React',
    'Angular',
    'JavaScript',
    'TypeScript',
    'HTML5',
    'CSS3',
    'SASS',
    'NextJS',
    'Vue.js',
    'Redux',
    'React Router',
    'Material-UI',
    'Bootstrap',
    'Tailwind CSS',
    'Webpack',
    'Vite',
    'ESLint',
    'Prettier',
    'Jest',
    'Cypress',
    'Figma',
    'Adobe XD',
    'Responsive Design',
    'Mobile First',
    'Progressive Web Apps',
    'Web Accessibility',
    'Git',
    'GitHub',
    'VS Code',
    'Chrome DevTools',
    'NPM',
    'Yarn',
    'REST APIs',
    'GraphQL',
    'Firebase',
    'Netlify',
    'Vercel',
    'Node.js',
    'Express',
    'MongoDB',
  ]
  const options = {
    radius: 300,
    maxSpeed: 'fast',
    initSpeed: 'fast',
    direction: 135,
    keep: true
  }
  // Initialize TagCloud when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      const element = document.querySelector(container)
      console.log('🔍 Looking for container:', container, 'Found:', !!element)

      if (element && isLoading) {
        element.innerHTML = ''
        console.log('📝 Skills to display:', texts)

        try {
          TagCloud(container, texts, options)
          console.log('✅ TagCloud initialized with', texts.length, 'skills:', texts.slice(0, 5).join(', '), '...')
          setLoad(false)
        } catch (error) {
          console.error('❌ TagCloud error:', error)
          // Show fallback skills list
          element.innerHTML = `
            <div style="color: #ff4500; text-align: center; padding: 20px; font-family: Poppins;">
              <h3 style="margin-bottom: 20px;">My Skills</h3>
              <div style="display: flex; flex-wrap: wrap; gap: 8px; justify-content: center; max-width: 400px; margin: 0 auto;">
                ${texts.map(skill => `
                  <span style="
                    background: rgba(255,69,0,0.1);
                    color: #ff4500;
                    padding: 6px 12px;
                    border-radius: 20px;
                    font-size: 13px;
                    border: 1px solid rgba(255,69,0,0.3);
                    cursor: pointer;
                    transition: all 0.3s ease;
                  "
                  onmouseover="this.style.background='rgba(0,255,127,0.1)'; this.style.color='#00ff7f'; this.style.borderColor='rgba(0,255,127,0.3)'"
                  onmouseout="this.style.background='rgba(255,69,0,0.1)'; this.style.color='#ff4500'; this.style.borderColor='rgba(255,69,0,0.3)'"
                  >${skill}</span>
                `).join('')}
              </div>
            </div>
          `
          setLoad(false)
        }
      }
    }, 500) // Increased delay to ensure DOM is ready

    return () => clearTimeout(timer)
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      const element = document.querySelector(container)
      if (element) {
        element.innerHTML = ''
      }
    }
  }, [])

  return (
    <div className="main">
      <span className="content" style={{
        display: 'block',
        width: '100%',
        height: '400px',
        minHeight: '400px'
      }}></span>
    </div>
  )
}

export default WordCloud
