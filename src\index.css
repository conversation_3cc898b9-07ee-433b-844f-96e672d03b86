html {
  font-size: 62.5%;
}

body {
  margin: 0;
  font:
    300 11px/1.4 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #252525;
  overflow: hidden;
}

/* Global Responsive Styles */
* {
  box-sizing: border-box;
}

/* Responsive Typography */
@media screen and (max-width: 768px) {
  html {
    font-size: 58%;
  }

  body {
    overflow-x: hidden;
    overflow-y: auto;
  }
}

@media screen and (max-width: 480px) {
  html {
    font-size: 55%;
  }
}

/* Responsive Images */
img {
  max-width: 100%;
  height: auto;
}

/* Responsive Containers */
@media screen and (max-width: 768px) {
  .container {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }
}

@media screen and (max-width: 480px) {
  .container {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
